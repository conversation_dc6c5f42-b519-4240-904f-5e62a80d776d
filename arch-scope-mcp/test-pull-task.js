#!/usr/bin/env node

/**
 * 测试MCP工具拉取任务功能
 */

// 设置环境变量
process.env.ARCHSCOPE_API_URL = 'http://localhost:8080';
process.env.ARCHSCOPE_API_TOKEN = 'dummy-token';
process.env.LOG_LEVEL = 'debug';

const { ArchScopeClient } = require('./dist/services/archscopeClient');
const { getConfig } = require('./dist/utils/config');

async function testPullTask() {
  console.log('🔧 测试MCP工具拉取任务...');

  try {
    // 加载配置
    const config = getConfig();
    console.log('配置:', {
      apiUrl: config.archscopeApiUrl,
      hasToken: !!config.archscopeApiToken,
      logLevel: config.logLevel
    });

    // 创建客户端
    const client = new ArchScopeClient(config);

    // 拉取任务
    const pullRequest = {
      workerId: 'test-worker-' + Date.now(),
      workerVersion: '1.0.0',
      supportedTaskTypes: ['PROJECT_ANALYSIS', 'CODE_PARSE', 'DOC_GENERATE'],
      maxConcurrentTasks: 1
    };

    console.log('拉取请求:', pullRequest);

    const response = await client.pullTask(pullRequest);
    console.log('✅ 拉取成功:', JSON.stringify(response, null, 2));

    return response;

  } catch (error) {
    console.error('❌ 拉取失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
      console.error('响应状态:', error.response.status);
    }
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testPullTask()
    .then(() => {
      console.log('🎉 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testPullTask };
