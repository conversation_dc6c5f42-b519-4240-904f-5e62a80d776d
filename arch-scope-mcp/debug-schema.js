#!/usr/bin/env node

/**
 * 调试MCP工具schema问题
 */

const { PullTaskResponseSchema } = require('./dist/types/schemas');

// 模拟API响应
const mockApiResponse = {
  "taskId": "9",
  "projectId": "1", 
  "taskType": "PROJECT_ANALYSIS",
  "priority": 1,
  "inputData": null,
  "createdAt": "2025-07-07T10:03:37",
  "timeoutAt": "2025-08-07T16:02:25.758203",
  "parameters": {},
  "hasTask": true,
  "message": null
};

console.log('🔍 调试MCP工具schema问题...');
console.log('模拟API响应:', JSON.stringify(mockApiResponse, null, 2));

try {
  const result = PullTaskResponseSchema.parse(mockApiResponse);
  console.log('✅ Schema验证成功:', JSON.stringify(result, null, 2));
} catch (error) {
  console.error('❌ Schema验证失败:', error.message);
  console.error('详细错误:', JSON.stringify(error.issues || error, null, 2));
}

// 测试没有任务的情况
const noTaskResponse = {
  "hasTask": false,
  "message": "没有可用的任务"
};

console.log('\n🔍 测试没有任务的情况...');
console.log('模拟API响应:', JSON.stringify(noTaskResponse, null, 2));

try {
  const result = PullTaskResponseSchema.parse(noTaskResponse);
  console.log('✅ Schema验证成功:', JSON.stringify(result, null, 2));
} catch (error) {
  console.error('❌ Schema验证失败:', error.message);
  console.error('详细错误:', JSON.stringify(error.issues || error, null, 2));
}
