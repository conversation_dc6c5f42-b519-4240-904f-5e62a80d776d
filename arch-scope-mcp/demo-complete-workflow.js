#!/usr/bin/env node

/**
 * ArchScope MCP工具完整工作流程演示
 * 演示任务拉取、处理和提交的完整流程
 */

// 设置环境变量
process.env.ARCHSCOPE_API_URL = 'http://localhost:8080';
process.env.ARCHSCOPE_API_TOKEN = 'dummy-token';
process.env.LOG_LEVEL = 'info';

const { ArchScopeClient } = require('./dist/services/archscopeClient');
const { getConfig } = require('./dist/utils/config');

/**
 * 生成代码分析文档
 */
function generateCodeAnalysis(taskData) {
  const { inputData } = taskData;
  const repoInfo = inputData?.repositoryInfo || {};
  
  return `# 代码分析报告

## 项目信息
- **仓库地址**: ${repoInfo.cloneUrl || 'N/A'}
- **分支**: ${repoInfo.branchName || 'main'}
- **提交ID**: ${repoInfo.commitId || 'N/A'}
- **分析时间**: ${new Date().toISOString()}

## 技术栈分析
- Java Spring Boot 框架
- Vue.js 前端框架
- MySQL 数据库
- Redis 缓存

## 架构模式
- 分层架构设计
- 前后端分离
- RESTful API
- 微服务架构

## 代码质量评估
- 代码结构清晰
- 遵循编码规范
- 异常处理完善
- 日志记录规范

## 改进建议
1. 增加单元测试覆盖率
2. 优化数据库查询性能
3. 加强代码注释
4. 完善API文档

---
*此报告由ArchScope自动生成*`;
}

/**
 * 生成项目分析文档
 */
function generateProjectAnalysis(taskData) {
  return `# 项目架构分析报告

## 系统概览
本项目采用现代化的分层架构设计，具有良好的可扩展性和维护性。

## 核心模块
- **应用层**: 处理HTTP请求和响应
- **领域层**: 核心业务逻辑
- **基础设施层**: 数据访问和外部服务
- **表现层**: 用户界面和API接口

## 技术选型
- **后端**: Java + Spring Boot
- **前端**: Vue.js + TypeScript
- **数据库**: MySQL + Redis
- **构建工具**: Maven + Vite

## 部署架构
- 容器化部署
- 负载均衡
- 数据库集群
- 缓存集群

## 性能指标
- 响应时间: < 200ms
- 并发用户: 1000+
- 可用性: 99.9%
- 数据一致性: 强一致性

---
*此报告由ArchScope自动生成*`;
}

/**
 * 完整工作流程演示
 */
async function demonstrateCompleteWorkflow() {
  console.log('🚀 开始ArchScope MCP工具完整工作流程演示...\n');

  try {
    // 1. 初始化客户端
    console.log('📋 步骤1: 初始化MCP客户端');
    const config = getConfig();
    const client = new ArchScopeClient(config);
    console.log('✅ 客户端初始化成功\n');

    // 2. 拉取任务
    console.log('📋 步骤2: 拉取待处理任务');
    const pullRequest = {
      workerId: 'demo-worker-' + Date.now(),
      workerVersion: '1.0.0',
      supportedTaskTypes: ['PROJECT_ANALYSIS', 'CODE_PARSE', 'DOC_GENERATE'],
      maxConcurrentTasks: 1
    };

    console.log('拉取请求:', pullRequest);
    const taskResponse = await client.pullTask(pullRequest);
    
    if (!taskResponse.hasTask) {
      console.log('❌ 没有可用的任务');
      return;
    }

    console.log('✅ 成功拉取任务:', {
      taskId: taskResponse.taskId,
      taskType: taskResponse.taskType,
      priority: taskResponse.priority
    });
    console.log('');

    // 3. 处理任务
    console.log('📋 步骤3: 处理任务');
    console.log('正在生成文档...');
    
    let documentContent;
    let documentType;
    let documentTitle;
    
    if (taskResponse.taskType === 'CODE_PARSE') {
      documentContent = generateCodeAnalysis(taskResponse);
      documentType = 'CODE_ANALYSIS';
      documentTitle = '代码分析报告';
    } else if (taskResponse.taskType === 'PROJECT_ANALYSIS') {
      documentContent = generateProjectAnalysis(taskResponse);
      documentType = 'PROJECT_ANALYSIS';
      documentTitle = '项目分析报告';
    } else {
      documentContent = generateCodeAnalysis(taskResponse);
      documentType = 'GENERAL_ANALYSIS';
      documentTitle = '通用分析报告';
    }

    console.log('✅ 文档生成完成\n');

    // 4. 提交结果
    console.log('📋 步骤4: 提交任务结果');
    const submitRequest = {
      taskId: taskResponse.taskId,
      overallStatus: 'COMPLETED',
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      executionTimeMs: 3000,
      results: [{
        documentType: documentType,
        documentTitle: documentTitle,
        documentContent: documentContent,
        filePath: `/docs/${documentType.toLowerCase()}.md`,
        status: 'SUCCESS'
      }],
      workerInfo: {
        workerId: pullRequest.workerId,
        workerVersion: pullRequest.workerVersion
      }
    };

    console.log('提交请求概要:', {
      taskId: submitRequest.taskId,
      status: submitRequest.overallStatus,
      resultsCount: submitRequest.results.length
    });

    const submitResponse = await client.submitResult(submitRequest);
    console.log('✅ 任务结果提交成功:', submitResponse);
    console.log('');

    // 5. 总结
    console.log('🎉 工作流程演示完成!');
    console.log('📊 处理统计:');
    console.log(`   - 任务ID: ${taskResponse.taskId}`);
    console.log(`   - 任务类型: ${taskResponse.taskType}`);
    console.log(`   - 处理状态: ${submitRequest.overallStatus}`);
    console.log(`   - 生成文档: ${submitRequest.results.length} 个`);
    console.log(`   - 工作节点: ${pullRequest.workerId}`);

  } catch (error) {
    console.error('❌ 工作流程演示失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    throw error;
  }
}

// 运行演示
if (require.main === module) {
  demonstrateCompleteWorkflow()
    .then(() => {
      console.log('\n✨ 演示成功完成!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 演示失败:', error);
      process.exit(1);
    });
}

module.exports = { demonstrateCompleteWorkflow };
