#!/usr/bin/env node

/**
 * 调试ArchScopeClient问题
 */

// 设置环境变量
process.env.ARCHSCOPE_API_URL = 'http://localhost:8080';
process.env.ARCHSCOPE_API_TOKEN = 'dummy-token';
process.env.LOG_LEVEL = 'debug';

const { ArchScopeClient } = require('./dist/services/archscopeClient');
const { getConfig } = require('./dist/utils/config');

async function debugClient() {
  console.log('🔧 调试ArchScopeClient...');

  try {
    // 加载配置
    const config = getConfig();
    console.log('配置:', {
      apiUrl: config.archscopeApiUrl,
      hasToken: !!config.archscopeApiToken,
      logLevel: config.logLevel
    });

    // 创建客户端
    const client = new ArchScopeClient(config);

    // 拉取任务
    const pullRequest = {
      workerId: 'debug-client-worker-' + Date.now(),
      workerVersion: '1.0.0',
      supportedTaskTypes: ['PROJECT_ANALYSIS', 'CODE_PARSE', 'DOC_GENERATE'],
      maxConcurrentTasks: 1
    };

    console.log('拉取请求:', pullRequest);

    const response = await client.pullTask(pullRequest);
    console.log('✅ 拉取成功:', JSON.stringify(response, null, 2));

    return response;

  } catch (error) {
    console.error('❌ 拉取失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
      console.error('响应状态:', error.response.status);
    }
    if (error.issues) {
      console.error('Schema验证错误:', JSON.stringify(error.issues, null, 2));
    }
    throw error;
  }
}

// 运行调试
if (require.main === module) {
  debugClient()
    .then(() => {
      console.log('🎉 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 调试失败:', error);
      process.exit(1);
    });
}

module.exports = { debugClient };
